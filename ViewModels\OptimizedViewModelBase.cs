using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace TeklaTool.ViewModels
{
    /// <summary>
    /// 优化的ViewModel基类，专门用于处理大量数据和高性能场景
    /// </summary>
    public abstract class OptimizedViewModelBase : INotifyPropertyChanged
    {
        private readonly Dictionary<string, object> _propertyValues = new Dictionary<string, object>();
        private readonly object _lockObject = new object();
        private readonly DispatcherTimer _updateTimer;
        private readonly HashSet<string> _pendingUpdates = new HashSet<string>();
        private bool _isUpdating;

        public event PropertyChangedEventHandler PropertyChanged;

        protected OptimizedViewModelBase()
        {
            // 初始化延迟更新计时器
            _updateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(50) // 50ms延迟批量更新
            };
            _updateTimer.Tick += UpdateTimer_Tick;
        }

        /// <summary>
        /// 设置属性值（优化版本，支持批量更新）
        /// </summary>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;

            // 将属性更新加入待处理队列
            lock (_lockObject)
            {
                _pendingUpdates.Add(propertyName);
            }

            // 启动延迟更新
            if (!_updateTimer.IsEnabled)
            {
                _updateTimer.Start();
            }

            return true;
        }

        /// <summary>
        /// 立即触发属性更改通知
        /// </summary>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            if (Application.Current?.Dispatcher.CheckAccess() == true)
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
            else
            {
                Application.Current?.Dispatcher.BeginInvoke(() =>
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName)));
            }
        }

        /// <summary>
        /// 批量触发属性更改通知
        /// </summary>
        protected void OnPropertiesChanged(params string[] propertyNames)
        {
            if (propertyNames == null || propertyNames.Length == 0) return;

            if (Application.Current?.Dispatcher.CheckAccess() == true)
            {
                foreach (var propertyName in propertyNames)
                {
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
                }
            }
            else
            {
                Application.Current?.Dispatcher.BeginInvoke(() =>
                {
                    foreach (var propertyName in propertyNames)
                    {
                        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
                    }
                });
            }
        }

        /// <summary>
        /// 延迟更新计时器事件处理
        /// </summary>
        private void UpdateTimer_Tick(object sender, EventArgs e)
        {
            _updateTimer.Stop();

            string[] updates;
            lock (_lockObject)
            {
                updates = _pendingUpdates.ToArray();
                _pendingUpdates.Clear();
            }

            if (updates.Length > 0)
            {
                OnPropertiesChanged(updates);
            }
        }

        /// <summary>
        /// 强制立即处理所有待处理的更新
        /// </summary>
        protected void FlushUpdates()
        {
            if (_updateTimer.IsEnabled)
            {
                _updateTimer.Stop();
                UpdateTimer_Tick(null, null);
            }
        }

        /// <summary>
        /// 暂停属性更新通知
        /// </summary>
        protected void SuspendUpdates()
        {
            _isUpdating = true;
            _updateTimer.Stop();
        }

        /// <summary>
        /// 恢复属性更新通知
        /// </summary>
        protected void ResumeUpdates()
        {
            _isUpdating = false;
            if (_pendingUpdates.Count > 0)
            {
                _updateTimer.Start();
            }
        }

        /// <summary>
        /// 检查是否正在更新
        /// </summary>
        protected bool IsUpdating => _isUpdating;

        /// <summary>
        /// 释放资源
        /// </summary>
        public virtual void Dispose()
        {
            _updateTimer?.Stop();
            lock (_lockObject)
            {
                _pendingUpdates.Clear();
            }
        }
    }

    /// <summary>
    /// 优化的ObservableCollection，支持批量操作和性能优化
    /// </summary>
    public class OptimizedObservableCollection<T> : ObservableCollection<T>
    {
        private bool _suppressNotification;

        /// <summary>
        /// 批量添加项目
        /// </summary>
        public void AddRange(IEnumerable<T> items)
        {
            if (items == null) return;

            _suppressNotification = true;
            try
            {
                foreach (var item in items)
                {
                    Items.Add(item);
                }
            }
            finally
            {
                _suppressNotification = false;
                OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));
                OnPropertyChanged(new PropertyChangedEventArgs(nameof(Count)));
            }
        }

        /// <summary>
        /// 批量移除项目
        /// </summary>
        public void RemoveRange(IEnumerable<T> items)
        {
            if (items == null) return;

            _suppressNotification = true;
            try
            {
                foreach (var item in items.ToList())
                {
                    Items.Remove(item);
                }
            }
            finally
            {
                _suppressNotification = false;
                OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));
                OnPropertyChanged(new PropertyChangedEventArgs(nameof(Count)));
            }
        }

        /// <summary>
        /// 替换所有项目
        /// </summary>
        public void ReplaceAll(IEnumerable<T> items)
        {
            if (items == null) return;

            _suppressNotification = true;
            try
            {
                Items.Clear();
                foreach (var item in items)
                {
                    Items.Add(item);
                }
            }
            finally
            {
                _suppressNotification = false;
                OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));
                OnPropertyChanged(new PropertyChangedEventArgs(nameof(Count)));
            }
        }

        protected override void OnCollectionChanged(NotifyCollectionChangedEventArgs e)
        {
            if (!_suppressNotification)
            {
                base.OnCollectionChanged(e);
            }
        }

        protected override void OnPropertyChanged(PropertyChangedEventArgs e)
        {
            if (!_suppressNotification)
            {
                base.OnPropertyChanged(e);
            }
        }
    }

    /// <summary>
    /// 异步命令实现
    /// </summary>
    public class AsyncRelayCommand : ICommand
    {
        private readonly Func<object, Task> _asyncExecute;
        private readonly Func<object, bool> _canExecute;
        private bool _isExecuting;

        public event EventHandler CanExecuteChanged;

        public AsyncRelayCommand(Func<Task> execute, Func<bool> canExecute = null)
            : this(execute != null ? (param) => execute() : null, canExecute != null ? (param) => canExecute() : null)
        {
        }

        public AsyncRelayCommand(Func<object, Task> execute, Func<object, bool> canExecute = null)
        {
            _asyncExecute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public bool CanExecute(object parameter)
        {
            return !_isExecuting && (_canExecute?.Invoke(parameter) ?? true);
        }

        public async void Execute(object parameter)
        {
            if (_isExecuting) return;

            _isExecuting = true;
            RaiseCanExecuteChanged();

            try
            {
                await _asyncExecute(parameter);
            }
            catch (Exception ex)
            {
                // 记录错误
                System.Diagnostics.Debug.WriteLine($"AsyncRelayCommand执行错误: {ex.Message}");
            }
            finally
            {
                _isExecuting = false;
                RaiseCanExecuteChanged();
            }
        }

        public void RaiseCanExecuteChanged()
        {
            CanExecuteChanged?.Invoke(this, EventArgs.Empty);
        }
    }
}
