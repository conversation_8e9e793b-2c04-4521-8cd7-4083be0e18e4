using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using TeklaTool.Models;
using TeklaTool.Services;
using TeklaTool.Utils;

namespace TeklaTool.ViewModels
{
    public class MainViewModel : OptimizedViewModelBase
    {
        private readonly TeklaModelService _teklaModelService;
        private readonly PartListViewModel _partListViewModel;
        private readonly AssemblyListViewModel _assemblyListViewModel;
        private bool _isLoading;
        private bool _isAssemblyMode;
        private string _statusText;
        private string _countText;
        private string _timeText;
        private string _searchText;
        private bool _isTopMost;
        private bool _enableHighlight;

        public PartListViewModel PartList => _partListViewModel;
        public AssemblyListViewModel AssemblyList => _assemblyListViewModel;

        public MainViewModel()
        {
            try
            {
                // 初始化服务和集合
                _teklaModelService = new TeklaModelService();
                _partListViewModel = new PartListViewModel(_teklaModelService, this);
                _assemblyListViewModel = new AssemblyListViewModel(_teklaModelService, this);
                _isLoading = false;
                _isAssemblyMode = false;
                _statusText = "就绪";
                _countText = "数量: 0";
                _timeText = "时间: 0s";
                _searchText = string.Empty;
                _isTopMost = false;
                _enableHighlight = true; // 默认启用高亮功能

                // 检查Tekla连接状态
                bool isConnected = _teklaModelService.GetConnectionStatus();
                if (!isConnected)
                {
                    _statusText = "未连接到Tekla模型，请确保Tekla Structures已启动并打开了模型";
                    Logger.LogWarning("初始化时未连接到Tekla模型");
                }

                // 初始化异步命令
                LoadAllPartsCommand = new AsyncRelayCommand(async () => await LoadAllParts(), () => !IsLoading);
                LoadSelectedPartsCommand = new AsyncRelayCommand(async () => await LoadSelectedParts(), () => !IsLoading);
                RefreshDataCommand = new AsyncRelayCommand(async () => await RefreshData(), () => !IsLoading);
                ToggleAssemblyModeCommand = new AsyncRelayCommand(async () => await ToggleAssemblyMode(), () => !IsLoading);
                SearchCommand = new RelayCommand(param => Search(), param => !IsLoading);
                ToggleTopMostCommand = new RelayCommand(param => ToggleTopMost());
                ToggleHighlightCommand = new RelayCommand(param => ToggleHighlight());

                // 高亮相关命令
                HighlightSelectedCommand = new AsyncRelayCommand(async () => await HighlightSelectedItems(), () => !IsLoading && EnableHighlight);
                ClearHighlightCommand = new AsyncRelayCommand(async () => await ClearHighlight(), () => !IsLoading);
            }
            catch (Exception ex)
            {
                // 确保即使初始化失败，也能创建基本对象
                Logger.LogError($"初始化MainViewModel时发生错误: {ex.Message}");
                _statusText = "初始化失败，请重启程序";

                // 确保所有字段都被初始化，防止空引用异常
                if (_teklaModelService == null) _teklaModelService = new TeklaModelService();

                // 初始化命令，即使它们可能无法正常工作
                LoadAllPartsCommand = new RelayCommand(async param => await LoadAllParts(), param => !IsLoading);
                LoadSelectedPartsCommand = new RelayCommand(async param => await LoadSelectedParts(), param => !IsLoading);
                RefreshDataCommand = new RelayCommand(async param => await RefreshData(), param => !IsLoading);
                ToggleAssemblyModeCommand = new RelayCommand(async param => await ToggleAssemblyMode(), param => !IsLoading);
                SearchCommand = new RelayCommand(param => Search(), param => !IsLoading);
                ToggleTopMostCommand = new RelayCommand(param => ToggleTopMost());
                ToggleHighlightCommand = new RelayCommand(param => ToggleHighlight());

                // 显示错误消息
                MessageBox.Show($"初始化程序时发生错误: {ex.Message}\n\n请确保Tekla Structures已启动并打开了模型。",
                    "初始化错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public bool IsAssemblyMode
        {
            get => _isAssemblyMode;
            set => SetProperty(ref _isAssemblyMode, value);
        }

        public string StatusText
        {
            get => _statusText;
            set => SetProperty(ref _statusText, value);
        }

        public string CountText
        {
            get => _countText;
            set => SetProperty(ref _countText, value);
        }

        public string TimeText
        {
            get => _timeText;
            set => SetProperty(ref _timeText, value);
        }

        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        public bool IsTopMost
        {
            get => _isTopMost;
            set => SetProperty(ref _isTopMost, value);
        }

        public bool EnableHighlight
        {
            get => _enableHighlight;
            set => SetProperty(ref _enableHighlight, value);
        }

        // 兼容旧绑定，后续可移除
        public IEnumerable<object> PartsView => PartList.PartsView;
        public IEnumerable<object> AssembliesView => AssemblyList.AssembliesView;
        public bool IsMergeRows
        {
            get => PartList.IsMergeRows;
            set
            {
                if (PartList.IsMergeRows != value)
                {
                    PartList.IsMergeRows = value;
                    AssemblyList.IsMergeRows = value; // 同时设置两个视图模型的合并行属性
                    OnPropertyChanged(nameof(IsMergeRows));
                    OnPropertyChanged(nameof(PartsView));
                    OnPropertyChanged(nameof(AssembliesView));
                }
            }
        }
        public ObservableCollection<TeklaModelPart> Parts => PartList.Parts;
        public ObservableCollection<PartListViewModel.MergedPartRow> MergedParts => PartList.MergedParts;
        public ObservableCollection<AssemblyInfo> Assemblies => AssemblyList.Assemblies;
        public ObservableCollection<AssemblyListViewModel.MergedAssemblyRow> MergedAssemblies => AssemblyList.MergedAssemblies;

        // 优化的异步命令
        public ICommand LoadAllPartsCommand { get; }
        public ICommand LoadSelectedPartsCommand { get; }
        public ICommand RefreshDataCommand { get; }
        public ICommand ToggleAssemblyModeCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ToggleTopMostCommand { get; }
        public ICommand ToggleHighlightCommand { get; }

        // 高亮相关的异步命令
        public ICommand HighlightSelectedCommand { get; }
        public ICommand ClearHighlightCommand { get; }

        private async Task LoadAllParts(bool useCache = true)
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                StatusText = "正在加载所有零件...";

                DateTime startTime = DateTime.Now;

                if (!_teklaModelService.GetConnectionStatus())
                {
                    MessageBox.Show("未连接到Tekla模型", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    StatusText = "未连接到Tekla模型";
                    return;
                }

                // 使用缓存机制
                List<TeklaModelPart> parts = await Task.Run(() => _teklaModelService.GetAllParts(useCache));

                if (IsAssemblyMode)
                {
                    await ProcessAssemblies(parts);
                }
                else
                {
                    PartList.SetParts(parts);
                }

                TimeSpan elapsed = DateTime.Now - startTime;
                CountText = $"数量: {(IsAssemblyMode ? Assemblies.Count : Parts.Count):N0}";
                TimeText = $"时间: {elapsed.TotalSeconds:F1}s";
                StatusText = "加载完成";
            }
            catch (Exception ex)
            {
                Logger.LogError($"加载所有零件时发生错误: {ex.Message}");
                MessageBox.Show($"加载所有零件时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText = "加载失败";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 刷新数据（清除缓存并重新加载）
        /// </summary>
        private async Task RefreshData()
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                StatusText = "正在刷新数据...";

                // 清除缓存
                _teklaModelService.ClearCache();

                // 重新加载数据（不使用缓存）
                await LoadAllParts(false);

                StatusText = "数据刷新完成";
            }
            catch (Exception ex)
            {
                Logger.LogError($"刷新数据时发生错误: {ex.Message}");
                MessageBox.Show($"刷新数据时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText = "刷新失败";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadSelectedParts()
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                StatusText = "正在加载选中零件...";

                DateTime startTime = DateTime.Now;

                if (!_teklaModelService.GetConnectionStatus())
                {
                    MessageBox.Show("未连接到Tekla模型", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    StatusText = "未连接到Tekla模型";
                    return;
                }

                List<TeklaModelPart> parts = await Task.Run(() => _teklaModelService.GetSelectedParts());

                if (parts.Count == 0)
                {
                    MessageBox.Show("未选中任何零件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    StatusText = "未选中任何零件";
                    IsLoading = false;
                    return;
                }

                if (IsAssemblyMode)
                {
                    await ProcessAssemblies(parts);
                }
                else
                {
                    PartList.SetParts(parts);
                }

                TimeSpan elapsed = DateTime.Now - startTime;
                CountText = $"数量: {(IsAssemblyMode ? Assemblies.Count : Parts.Count):N0}";
                TimeText = $"时间: {elapsed.TotalSeconds:F1}s";
                StatusText = "加载完成";
            }
            catch (Exception ex)
            {
                Logger.LogError($"加载选中零件时发生错误: {ex.Message}");
                MessageBox.Show($"加载选中零件时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText = "加载失败";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ToggleAssemblyMode()
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                StatusText = "切换模式中...";

                IsAssemblyMode = !IsAssemblyMode;

                // 如果有数据，重新处理
                if (Parts.Count > 0)
                {
                    List<TeklaModelPart> parts = Parts.ToList();
                    if (IsAssemblyMode)
                    {
                        await ProcessAssemblies(parts);
                    }
                    else
                    {
                        // 切换回零件模式，不需要特殊处理
                    }
                }

                // 通知属性变更，触发MainWindow中的PropertyChanged事件处理程序
                // 这将导致重新应用默认排序
                OnPropertyChanged(nameof(IsAssemblyMode));

                StatusText = $"已切换到{(IsAssemblyMode ? "构件" : "零件")}模式";
            }
            catch (Exception ex)
            {
                Logger.LogError($"切换模式时发生错误: {ex.Message}");
                MessageBox.Show($"切换模式时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText = "切换失败";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ProcessAssemblies(List<TeklaModelPart> parts)
        {
            try
            {
                StatusText = "正在处理构件数据...";

                List<AssemblyInfo> assemblies = await Task.Run(() => _teklaModelService.GetAssemblies(parts));

                Application.Current.Dispatcher.Invoke(() =>
                {
                    Assemblies.Clear();
                    foreach (var assembly in assemblies)
                    {
                        Assemblies.Add(assembly);
                    }
                });

                StatusText = "构件数据处理完成";
            }
            catch (Exception ex)
            {
                Logger.LogError($"处理构件数据时发生错误: {ex.Message}");
                MessageBox.Show($"处理构件数据时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText = "处理失败";
            }
        }

        private void Search()
        {
            try
            {
                StatusText = "正在搜索...";

                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    StatusText = "搜索文本为空";
                    return;
                }

                // 搜索逻辑
                var searchResults = Parts.Where(p => !string.IsNullOrEmpty(p.Name) && p.Name.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0).ToList();

                if (searchResults.Count == 0)
                {
                    StatusText = "未找到匹配项";
                }
                else
                {
                    StatusText = $"找到{searchResults.Count}个匹配项";
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"搜索时发生错误: {ex.Message}");
                MessageBox.Show($"搜索时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText = "搜索失败";
            }
        }

        private void ToggleTopMost()
        {
            IsTopMost = !IsTopMost;
        }

        private void ToggleHighlight()
        {
            EnableHighlight = !EnableHighlight;
        }

        /// <summary>
        /// 高亮选中的项目（异步优化版本）
        /// </summary>
        private async Task HighlightSelectedItems()
        {
            if (!EnableHighlight)
            {
                StatusText = "高亮功能已禁用";
                return;
            }

            try
            {
                StatusText = "正在高亮选中项目...";

                // 根据当前模式获取选中项目的ID
                List<int> objectIds = new List<int>();

                if (IsAssemblyMode)
                {
                    // 构件模式：获取选中构件的所有零件ID
                    var selectedAssemblies = AssemblyList.GetSelectedAssemblies();
                    if (selectedAssemblies.Any())
                    {
                        var assemblyNumbers = selectedAssemblies.Select(a => a.AssemblyNumber).ToList();
                        bool result = await Task.Run(() => _teklaModelService.HighlightAssembliesUsingCache(assemblyNumbers));

                        StatusText = result ? $"已高亮 {selectedAssemblies.Count} 个构件" : "高亮失败";
                        return;
                    }
                }
                else
                {
                    // 零件模式：获取选中零件的ID
                    var selectedParts = PartList.GetSelectedParts();
                    if (selectedParts.Any())
                    {
                        objectIds = selectedParts.Where(p => p.ModelObjectId.HasValue)
                                                .Select(p => p.ModelObjectId.Value)
                                                .ToList();
                    }
                }

                if (objectIds.Count > 0)
                {
                    bool result = await _teklaModelService.HighlightObjectsAsync(objectIds);
                    StatusText = result ? $"已高亮 {objectIds.Count} 个零件" : "高亮失败";
                }
                else
                {
                    StatusText = "未选中任何项目";
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"高亮选中项目时发生错误: {ex.Message}");
                StatusText = "高亮失败";
            }
        }

        /// <summary>
        /// 清除高亮
        /// </summary>
        private async Task ClearHighlight()
        {
            try
            {
                StatusText = "正在清除高亮...";

                bool result = await _teklaModelService.HighlightObjectsAsync(new List<int>());
                StatusText = result ? "已清除高亮" : "清除高亮失败";
            }
            catch (Exception ex)
            {
                Logger.LogError($"清除高亮时发生错误: {ex.Message}");
                StatusText = "清除高亮失败";
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public override void Dispose()
        {
            base.Dispose();
            _partListViewModel?.Dispose();
            _assemblyListViewModel?.Dispose();
        }
    }
}
