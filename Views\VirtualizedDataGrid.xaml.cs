using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Threading;

namespace TeklaTool.Views
{
    /// <summary>
    /// 优化的虚拟化DataGrid控件，专门用于处理大量数据
    /// </summary>
    public partial class VirtualizedDataGrid : UserControl
    {
        // 依赖属性
        public static readonly DependencyProperty ItemsSourceProperty =
            DependencyProperty.Register("ItemsSource", typeof(IEnumerable), typeof(VirtualizedDataGrid),
                new PropertyMetadata(null, OnItemsSourceChanged));

        public static readonly DependencyProperty IsLoadingProperty =
            DependencyProperty.Register("IsLoading", typeof(bool), typeof(VirtualizedDataGrid),
                new PropertyMetadata(false, OnIsLoadingChanged));

        public static readonly DependencyProperty SelectedItemsProperty =
            DependencyProperty.Register("SelectedItems", typeof(IList), typeof(VirtualizedDataGrid),
                new PropertyMetadata(null));

        // 属性
        public IEnumerable ItemsSource
        {
            get => (IEnumerable)GetValue(ItemsSourceProperty);
            set => SetValue(ItemsSourceProperty, value);
        }

        public bool IsLoading
        {
            get => (bool)GetValue(IsLoadingProperty);
            set => SetValue(IsLoadingProperty, value);
        }

        public IList SelectedItems
        {
            get => (IList)GetValue(SelectedItemsProperty);
            set => SetValue(SelectedItemsProperty, value);
        }

        // 事件
        public event EventHandler<SelectionChangedEventArgs> SelectionChanged;
        public event EventHandler<VirtualizedDataGridRowEventArgs> RowDoubleClick;

        // 内部字段
        private ICollectionView _collectionView;
        private DispatcherTimer _updateTimer;
        private bool _isUpdating;

        public VirtualizedDataGrid()
        {
            InitializeComponent();
            InitializeTimer();
            SetupEventHandlers();
        }

        private void InitializeTimer()
        {
            _updateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100)
            };
            _updateTimer.Tick += UpdateTimer_Tick;
        }

        private void SetupEventHandlers()
        {
            MainDataGrid.SelectionChanged += MainDataGrid_SelectionChanged;
            MainDataGrid.MouseDoubleClick += MainDataGrid_MouseDoubleClick;
            MainDataGrid.LoadingRow += MainDataGrid_LoadingRow;
            MainDataGrid.UnloadingRow += MainDataGrid_UnloadingRow;
        }

        private static void OnItemsSourceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = (VirtualizedDataGrid)d;
            control.UpdateItemsSource();
        }

        private static void OnIsLoadingChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = (VirtualizedDataGrid)d;
            control.UpdateLoadingState();
        }

        private async void UpdateItemsSource()
        {
            if (_isUpdating) return;
            _isUpdating = true;

            try
            {
                await Task.Run(() =>
                {
                    // 在后台线程中准备数据
                    Dispatcher.BeginInvoke(() =>
                    {
                        try
                        {
                            MainDataGrid.ItemsSource = ItemsSource;

                            if (ItemsSource != null)
                            {
                                _collectionView = CollectionViewSource.GetDefaultView(ItemsSource);

                                // 启用延迟更新以提高性能
                                if (_collectionView is ListCollectionView listView)
                                {
                                    listView.IsLiveFiltering = false;
                                    listView.IsLiveSorting = false;
                                    listView.IsLiveGrouping = false;
                                }

                                GenerateColumns();
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"[VirtualizedDataGrid] 更新数据源时出错: {ex.Message}");
                        }
                    });
                });
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void GenerateColumns()
        {
            if (ItemsSource == null) return;

            MainDataGrid.Columns.Clear();

            // 获取第一个项目来确定列
            var firstItem = ItemsSource.Cast<object>().FirstOrDefault();
            if (firstItem == null) return;

            var properties = TypeDescriptor.GetProperties(firstItem);

            foreach (PropertyDescriptor property in properties)
            {
                // 跳过复杂类型
                if (!IsSimpleType(property.PropertyType)) continue;

                var column = new DataGridTextColumn
                {
                    Header = GetColumnHeader(property.Name),
                    Binding = new Binding(property.Name),
                    Width = GetColumnWidth(property.Name),
                    IsReadOnly = true
                };

                MainDataGrid.Columns.Add(column);
            }
        }

        private bool IsSimpleType(Type type)
        {
            return type.IsPrimitive ||
                   type == typeof(string) ||
                   type == typeof(DateTime) ||
                   type == typeof(decimal) ||
                   type == typeof(Guid) ||
                   (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>) &&
                    IsSimpleType(type.GetGenericArguments()[0]));
        }

        private string GetColumnHeader(string propertyName)
        {
            // 可以在这里添加本地化逻辑
            return propertyName switch
            {
                "Index" => "序号",
                "PartNumber" => "零件编号",
                "Name" => "名称",
                "Profile" => "截面",
                "Material" => "材质",
                "Finish" => "表面处理",
                "Class" => "等级",
                "Phase" => "阶段",
                "Count" => "数量",
                "AssemblyNumber" => "构件编号",
                "Remark" => "备注",
                _ => propertyName
            };
        }

        private DataGridLength GetColumnWidth(string propertyName)
        {
            return propertyName switch
            {
                "Index" => new DataGridLength(60),
                "PartNumber" => new DataGridLength(120),
                "Name" => new DataGridLength(150),
                "Profile" => new DataGridLength(100),
                "Material" => new DataGridLength(80),
                "Finish" => new DataGridLength(80),
                "Class" => new DataGridLength(60),
                "Phase" => new DataGridLength(60),
                "Count" => new DataGridLength(60),
                "AssemblyNumber" => new DataGridLength(120),
                "Remark" => new DataGridLength(200),
                _ => DataGridLength.Auto
            };
        }

        private void UpdateLoadingState()
        {
            LoadingOverlay.Visibility = IsLoading ? Visibility.Visible : Visibility.Collapsed;
            MainDataGrid.IsEnabled = !IsLoading;
        }

        private void MainDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            SelectedItems = MainDataGrid.SelectedItems;
            SelectionChanged?.Invoke(this, e);
        }

        private void MainDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (MainDataGrid.SelectedItem != null)
            {
                var row = MainDataGrid.ItemContainerGenerator.ContainerFromItem(MainDataGrid.SelectedItem) as DataGridRow;
                if (row != null)
                {
                    RowDoubleClick?.Invoke(this, new VirtualizedDataGridRowEventArgs(row));
                }
            }
        }

        private void MainDataGrid_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            // 优化行加载性能
            e.Row.Header = null; // 移除行头以提高性能
        }

        private void MainDataGrid_UnloadingRow(object sender, DataGridRowEventArgs e)
        {
            // 清理资源
        }

        private void UpdateTimer_Tick(object sender, EventArgs e)
        {
            _updateTimer.Stop();

            // 执行延迟更新
            if (_collectionView != null)
            {
                _collectionView.Refresh();
            }
        }

        // 公共方法
        public void RefreshData()
        {
            if (_collectionView != null)
            {
                _collectionView.Refresh();
            }
        }

        public void ScrollToItem(object item)
        {
            if (item != null)
            {
                MainDataGrid.ScrollIntoView(item);
            }
        }

        public void ClearSelection()
        {
            MainDataGrid.SelectedItems.Clear();
        }
    }

    /// <summary>
    /// 虚拟化DataGrid行事件参数
    /// </summary>
    public class VirtualizedDataGridRowEventArgs : EventArgs
    {
        public DataGridRow Row { get; }

        public VirtualizedDataGridRowEventArgs(DataGridRow row)
        {
            Row = row;
        }
    }
}
