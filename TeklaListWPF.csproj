﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
    <RootNamespace>TeklaTool</RootNamespace>
    <AssemblyName>TeklaListWPF</AssemblyName>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="Tekla.Structures">
      <HintPath>bin\Debug\net48\Tekla.Structures.dll</HintPath>
    </Reference>
    <Reference Include="Tekla.Structures.Drawing">
      <HintPath>bin\Debug\net48\Tekla.Structures.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="Tekla.Structures.Model">
      <HintPath>bin\Debug\net48\Tekla.Structures.Model.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Utils\" />
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
  </ItemGroup>

</Project>
